import pulp
import pandas as pd
import math
import os
from datetime import datetime, timedelta


# ================== 1. 加载数据 ==================
df_movies = pd.read_csv("df_movies_schedule.csv")
df_cinema = pd.read_csv("df_cinema.csv")

# 数据清洗：确保字段名正确（根据实际数据调整）
df_movies.columns = df_movies.columns.str.strip()
df_cinema.columns = df_cinema.columns.str.strip()

# ================== 2. 参数定义 ==================
rooms = df_cinema['room'].tolist()
movie_ids = df_movies['id'].tolist()
adjusted_runtimes = {
    i: math.ceil(df_movies[df_movies['id'] == i]['runtime'].values[0] / 30) * 30
    for i in movie_ids
}

# 生成时间点：10:00 到 27:00（次日凌晨3:00），15分钟间隔
def time_str_to_minutes(time_str):
    """将 'HH:MM' 转换为从 00:00 开始的总分钟数（支持超过24小时）"""
    h, m = map(int, time_str.split(":"))
    return h * 60 + m

def minutes_to_time_str(total_minutes):
    """将总分钟数转换为 'HH:MM' 格式（自动取模到24小时制显示）"""
    total_minutes %= (24 * 60)  # 转为当天时间显示
    h = total_minutes // 60
    m = total_minutes % 60
    return f"{h:02d}:{m:02d}"

# 定义开始和结束时间（以分钟为单位）
start_minutes = time_str_to_minutes("10:00")  # 600
end_minutes = time_str_to_minutes("27:00")    # 1620（= 24*60 + 3*60）

# 生成时间点
times = []
current = start_minutes
while current <= end_minutes:
    times.append(minutes_to_time_str(current))
    current += 15


versions = ["2D", "3D", "IMAX"]

# 基础票价和成本参数
BASIC_PRICE = 1  # 动态计算
BASIC_COST = 2.42
FIXED_COST = 90

# 题材播放次数限制
THEME_LIMITS = {
    "Animation": {"min": 1, "max": 5},
    "Horror": {"min": 0, "max": 3},
    "Action": {"min": 2, "max": 6},
    "Drama": {"min": 1, "max": 6}
}

# 题材播放时间限制
THEME_TIME_LIMITS = {
    "Animation": {"start": "10:00", "end": "19:00"},
    "Family": {"start": "10:00", "end": "19:00"},
    "Horror": {"start": "21:00", "end": "27:00"},
    "Thriller": {"start": "21:00", "end": "27:00"}
}

# 3D/IMAX 总时长限制（分钟）
MAX_3D_TIME = 1200
MAX_IMAX_TIME = 1500

# ================== 3. 构建模型 ==================
prob = pulp.LpProblem("Cinema_Scheduling", pulp.LpMaximize)

x = {}
for i in movie_ids:
    for j in rooms:
        for t in times:
            for v in versions:
                # 构造合法变量名：替换 : 为 _，避免所有特殊字符
                var_name = f"x_{i}_{j}_{t.replace(':', '')}_{v}"
                # 例如：x_180219_R01_0000_2D
                x[(i, j, t, v)] = pulp.LpVariable(var_name, cat='Binary')


# y变量已移除，因为新的约束逻辑不再需要它


# ================== 4. 目标函数：最大化净收益 ==================
total_profit = 0

for i in movie_ids:
    movie = df_movies[df_movies['id'] == i].iloc[0]
    basic_price = movie['basic_price']
    rating = movie['rating']
    adjusted_runtime = adjusted_runtimes[i]
    genres = movie['genres'].split('|') if isinstance(movie['genres'], str) else []
    original_language = movie['original_language'].split(',') if isinstance(movie['original_language'], str) else []

    is_domestic = 'Mandarin' in original_language  # 判断是否国产电影
    share_rate = 0.43 if is_domestic else 0.51

    for j in rooms:
        hall = df_cinema[df_cinema['room'] == j].iloc[0]
        capacity = hall['capacity']

        for t in times:
            t_obj = datetime.strptime(t, "%H:%M")
            is_golden = 18 <= t_obj.hour <= 21  # 黄金时段

            for v in versions:
                if hall[v] == 0:  # 放映厅不支持该版本
                    continue

                # 计算票价
                price_multiplier = 1.0
                if v == "3D":
                    price_multiplier *= 1.2
                elif v == "IMAX":
                    price_multiplier *= 1.23
                if is_golden:
                    price_multiplier *= 1.3
                ticket_price = basic_price * price_multiplier

                # 计算上座人数
                attendance = int(capacity * (rating / 10))  # 向下取整

                # 计算播放成本
                version_coeff = 1.0
                if v == "3D":
                    version_coeff = 1.1
                elif v == "IMAX":
                    version_coeff = 1.15
                cost = version_coeff * capacity * BASIC_COST + FIXED_COST

                # 净收益 = 票房 × (1 - 分成比例) - 成本
                revenue = ticket_price * attendance * (1 - share_rate)
                profit = revenue - cost

                total_profit += profit * x[i, j, t, v]

prob.setObjective(total_profit)

# ================== 5. 添加约束 ==================

# (1) 放映厅版本兼容性（已在变量定义中跳过，可省略）

# (2) 时间间隔约束：同一放映厅两场电影间隔 ≥15分钟
# 修正后的时间重叠约束：直接禁止冲突的排片
for j in rooms:
    for i in movie_ids:
        for v in versions:
            # 检查放映厅是否支持该版本
            if df_cinema[df_cinema['room'] == j].iloc[0][v] == 0:
                continue

            adjusted_runtime = adjusted_runtimes[i]
            for t1 in times:
                t1_min = time_str_to_minutes(t1)
                end_time = t1_min + adjusted_runtime + 15  # 播放结束时间（含15分钟间隔）

                # 如果电影i在厅j的t1时间播放，那么在占用时间段内不能播放其他任何电影
                for t2 in times:
                    t2_min = time_str_to_minutes(t2)
                    if t1_min < t2_min < end_time:  # t2在播放时间段内（不包括开始时间）
                        # 禁止在t2时间播放任何电影
                        for i2 in movie_ids:
                            for v2 in versions:
                                if df_cinema[df_cinema['room'] == j].iloc[0][v2] == 0:
                                    continue
                                prob += x[i2, j, t2, v2] <= 1 - x[i, j, t1, v]


# (3) 3D/IMAX 总时长限制
total_3d_time = pulp.lpSum([
    adjusted_runtimes[i] * x[i, j, t, "3D"]
    for i in movie_ids for j in rooms for t in times
])
total_imax_time = pulp.lpSum([
    adjusted_runtimes[i] * x[i, j, t, "IMAX"]
    for i in movie_ids for j in rooms for t in times
])
prob += total_3d_time <= MAX_3D_TIME
prob += total_imax_time <= MAX_IMAX_TIME

# (4) 设备连续运行限制：9小时窗口内播放时长 ≤7小时（420分钟）
for j in rooms:
    for window_start in times:
        window_start_min = time_str_to_minutes(window_start)
        window_end_min = window_start_min + 9 * 60  # 9小时后
        if window_end_min > end_minutes:
            continue
        total_runtime_in_window = pulp.lpSum([
            adjusted_runtimes[i] * x[i, j, t, v]
            for i in movie_ids for t in times for v in versions
            if window_start_min <= time_str_to_minutes(t) <= window_end_min
        ])
        prob += total_runtime_in_window <= 420  # 7小时 = 420分钟

# (5) 题材播放次数限制
for theme, limits in THEME_LIMITS.items():
    count = pulp.lpSum([
        x[i, j, t, v]
        for i in movie_ids for j in rooms for t in times for v in versions
        if theme in df_movies.loc[df_movies['id'] == i, 'genres'].values[0]
    ])
    prob += count >= limits['min']
    prob += count <= limits['max']

# (6) 题材播放时间限制
for i in movie_ids:
    movie = df_movies[df_movies['id'] == i].iloc[0]
    genres = movie['genres'].split('|') if isinstance(movie['genres'], str) else []
    for j in rooms:
        for t in times:
            t_min = time_str_to_minutes(t)
            for v in versions:
                if df_cinema[df_cinema['room'] == j].iloc[0][v] == 0:
                    continue
                for theme in genres:
                    if theme in THEME_TIME_LIMITS:
                        time_limits = THEME_TIME_LIMITS[theme]
                        start = time_str_to_minutes(time_limits['start'])  # 支持 27:00
                        end = time_str_to_minutes(time_limits['end'])      # 支持 27:00
                        if not (start <= t_min <= end):
                            prob += x[i, j, t, v] == 0

# 约束 1: 同一放映厅在同一时间只能播放一部电影（无论版本）
for j in rooms:
    for t in times:
        prob += pulp.lpSum([x[i, j, t, v] for i in movie_ids for v in versions
                           if df_cinema[df_cinema['room'] == j].iloc[0][v] == 1]) <= 1

# 约束 2: 同一部电影在同一放映厅只能选择一个版本播放（整个排片周期内）
for i in movie_ids:
    for j in rooms:
        # 获取该厅支持的版本
        supported_versions = [v for v in versions if df_cinema[df_cinema['room'] == j].iloc[0][v] == 1]

        # 对于每对不同的版本，不能同时播放
        for v1_idx, v1 in enumerate(supported_versions):
            for v2 in supported_versions[v1_idx + 1:]:
                total_v1 = pulp.lpSum([x[i, j, t, v1] for t in times])
                total_v2 = pulp.lpSum([x[i, j, t, v2] for t in times])
                # 两个版本不能同时被选择
                prob += total_v1 + total_v2 <= 1

# ================== 6. 求解 ==================

try:
    prob.solve(pulp.SCIP_CMD(
        msg=1,
        keepFiles=True,  # 保留临时文件（可选）
        options=[
            '-S', 'Cinema_Scheduling-pulp.set',
            '-c', 'set limits time 7200',
            '-c', 'set limits gap 0.03',
            '-c', 'write solution Cinema_Scheduling-pulp.sol'
        ]
    ))
    print("求解正常结束")
except Exception as e:
    print(f"求解过程中断: {e}")

sol_file = "Cinema_Scheduling-pulp.sol"
print(f"解文件路径: {sol_file}")

status = prob.status
print("求解状态:", pulp.LpStatus[status])

if (status == pulp.LpStatusOptimal) or (os.path.exists(sol_file)):
    if os.path.exists(sol_file):
        print("正在从 .sol 文件加载变量值...")
        loaded_vars = 0  # 计数器
        with open(sol_file, 'r') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                # 调试输出
                print(f"解析行 {line_num}: {line}")  
            
                # 跳过空行、注释行及状态行
                if (not line 
                    or line.startswith('#') 
                    or 'status:' in line.lower()
                    or 'objective:' in line.lower()):
                    print(f"跳过行 {line_num}: 注释/状态行")
                    continue
            
                parts = line.split()
                if len(parts) < 2:
                    print(f"跳过行 {line_num}: 字段不足")
                    continue
            
                raw_var_name = parts[0]
                try:
                    var_value = float(parts[1])
                except ValueError:
                    print(f"错误：行 {line_num} 的值无效 '{parts[1]}'")
                    continue
            
                # 变量名格式转换
                formatted_var_name = raw_var_name.replace('_', ',', 3).replace('x,', 'x(') + ")"
            
                # 检查变量是否存在
                if formatted_var_name in prob.variablesDict():
                    prob.variablesDict()[formatted_var_name].varValue = var_value
                    loaded_vars += 1
                else:
                    print(f"警告：变量 {formatted_var_name} 未在模型中定义")
    
        print(f"成功加载 {loaded_vars} 个变量值")
    else:
        print("解文件不存在")
else:
    print("未找到可行解，请检查模型或参数！")

# === 无论如何都尝试输出结果 ===
print("求解状态:", pulp.LpStatus[prob.status])
print("目标值:", pulp.value(prob.objective) if prob.objective else "NaN")

# 关键：检查 x[i,j,t,v].varValue 是否有值
results = []
found_any = False

for i in movie_ids:
    for j in rooms:
        for t in times:
            for v in versions:
                if df_cinema[df_cinema['room'] == j].iloc[0][v] == 0:
                    continue
                var = x[i, j, t, v]
                # 打印调试信息（可选）
                # print(f"Var {var.name}: {var.varValue}")
                if var.varValue is not None and var.varValue > 0.5:
                    hall = df_cinema[df_cinema['room'] == j].iloc[0]
                    movie = df_movies[df_movies['id'] == i].iloc[0]
                    attendance = int(hall['capacity'] * (movie['rating'] / 10))
                    results.append({
                        "room": j,
                        "showtime": t,
                        "id": i,
                        "version": v,
                        "attendance": attendance,
                    })
                    found_any = True

if not found_any:
    print("没有找到任何排片！可能 varValue 未被赋值")
    print("请检查：SCIP 是否返回了解？PuLP 是否更新了 varValue？")
else:
    print(f"找到 {len(results)} 场排片")

result_df = pd.DataFrame(results)
print("\n排片方案:")
print(result_df)

result_df.to_csv("optimized_schedule.csv", index=False)
print("已保存到 optimized_schedule.csv")

# ================== 7. 输出结果 ==================


#results = []

#for i in movie_ids:
 #   for j in rooms:
  #      for t in times:
   #         for v in versions:
    #            if df_cinema[df_cinema['room'] == j].iloc[0][v] == 0:
     #               continue
      #          if x[i, j, t, v].varValue is not None and x[i, j, t, v].varValue > 0.5:
       #             hall = df_cinema[df_cinema['room'] == j].iloc[0]
        #            movie = df_movies[df_movies['id'] == i].iloc[0]
         #           attendance = int(hall['capacity'] * (movie['rating'] / 10))
          #          results.append({
           #             "room": j,
            #            "showtime": t,
             #           "id": i,
              #          "version": v,
               #         "attendance": attendance,
                #    })

#result_df = pd.DataFrame(results)
#print(result_df)
#result_df.to_csv("optimized_schedule.csv", index=False)
