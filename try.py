import pulp
import pandas as pd
import math
from datetime import datetime


# ================== 1. 加载数据 ==================
df_movies = pd.read_csv("df_movies_schedule.csv")
df_cinema = pd.read_csv("df_cinema.csv")

# 数据清洗：确保字段名正确（根据实际数据调整）
df_movies.columns = df_movies.columns.str.strip()
df_cinema.columns = df_cinema.columns.str.strip()

# ================== 2. 参数定义 ==================
rooms = df_cinema['room'].tolist()
movie_ids = df_movies['id'].tolist()
adjusted_runtimes = {
    i: math.ceil(df_movies[df_movies['id'] == i]['runtime'].values[0] / 30) * 30
    for i in movie_ids
}

# 生成时间点：10:00 到 27:00（次日凌晨3:00），15分钟间隔
def time_str_to_minutes(time_str):
    """将 'HH:MM' 转换为从 00:00 开始的总分钟数（支持超过24小时）"""
    h, m = map(int, time_str.split(":"))
    return h * 60 + m

def minutes_to_time_str(total_minutes):
    """将总分钟数转换为 'HH:MM' 格式（自动取模到24小时制显示）"""
    total_minutes %= (24 * 60)  # 转为当天时间显示
    h = total_minutes // 60
    m = total_minutes % 60
    return f"{h:02d}:{m:02d}"

# 定义开始和结束时间（以分钟为单位）
start_minutes = time_str_to_minutes("10:00")  # 600
end_minutes = time_str_to_minutes("27:00")    # 1620（= 24*60 + 3*60）

# 生成时间点
times = []
current = start_minutes
while current <= end_minutes:
    times.append(minutes_to_time_str(current))
    current += 15


versions = ["2D", "3D", "IMAX"]

# 基础票价和成本参数
BASIC_PRICE = 1  # 动态计算
BASIC_COST = 2.42
FIXED_COST = 90

# 题材播放次数限制
THEME_LIMITS = {
    "Animation": {"min": 1, "max": 5},
    "Horror": {"min": 0, "max": 3},
    "Action": {"min": 2, "max": 6},
    "Drama": {"min": 1, "max": 6}
}

# 题材播放时间限制
THEME_TIME_LIMITS = {
    "Animation": {"start": "10:00", "end": "19:00"},
    "Family": {"start": "10:00", "end": "19:00"},
    "Horror": {"start": "21:00", "end": "27:00"},
    "Thriller": {"start": "21:00", "end": "27:00"}
}

# 3D/IMAX 总时长限制（分钟）
MAX_3D_TIME = 1200
MAX_IMAX_TIME = 1500

# ================== 3. 构建模型 ==================
prob = pulp.LpProblem("Cinema_Scheduling", pulp.LpMaximize)

x = {}
for i in movie_ids:
    for j in rooms:
        for t in times:
            for v in versions:
                # 构造合法变量名：替换 : 为 _，避免所有特殊字符
                var_name = f"x_{i}_{j}_{t.replace(':', '')}_{v}"
                # 例如：x_180219_R01_0000_2D
                x[(i, j, t, v)] = pulp.LpVariable(var_name, cat='Binary')


# y变量已移除，因为新的约束逻辑不再需要它


# ================== 4. 目标函数：最大化净收益 ==================
total_profit = 0

for i in movie_ids:
    movie = df_movies[df_movies['id'] == i].iloc[0]
    basic_price = movie['basic_price']
    rating = movie['rating']
    adjusted_runtime = adjusted_runtimes[i]
    genres = movie['genres'].split('|') if isinstance(movie['genres'], str) else []
    original_language = movie['original_language'].split(',') if isinstance(movie['original_language'], str) else []

    is_domestic = 'Mandarin' in original_language  # 判断是否国产电影
    share_rate = 0.43 if is_domestic else 0.51

    for j in rooms:
        hall = df_cinema[df_cinema['room'] == j].iloc[0]
        capacity = hall['capacity']

        for t in times:
            t_obj = datetime.strptime(t, "%H:%M")
            is_golden = 18 <= t_obj.hour <= 21  # 黄金时段

            for v in versions:
                if hall[v] == 0:  # 放映厅不支持该版本
                    continue

                # 计算票价
                price_multiplier = 1.0
                if v == "3D":
                    price_multiplier *= 1.2
                elif v == "IMAX":
                    price_multiplier *= 1.23
                if is_golden:
                    price_multiplier *= 1.3
                ticket_price = basic_price * price_multiplier

                # 计算上座人数
                attendance = int(capacity * (rating / 10))  # 向下取整

                # 计算播放成本
                version_coeff = 1.0
                if v == "3D":
                    version_coeff = 1.1
                elif v == "IMAX":
                    version_coeff = 1.15
                cost = version_coeff * capacity * BASIC_COST + FIXED_COST

                # 净收益 = 票房 × (1 - 分成比例) - 成本
                revenue = ticket_price * attendance * (1 - share_rate)
                profit = revenue - cost

                total_profit += profit * x[i, j, t, v]

prob.setObjective(total_profit)

# ================== 5. 添加约束 ==================

# (1) 放映厅版本兼容性（已在变量定义中跳过，可省略）

# (2) 简化的时间间隔约束：同一放映厅在任意时间只能播放一部电影
# 这个约束已经在后面的"约束1"中实现，这里可以省略复杂的时间重叠检查


# (3) 3D/IMAX 总时长限制
total_3d_time = pulp.lpSum([
    adjusted_runtimes[i] * x[i, j, t, "3D"]
    for i in movie_ids for j in rooms for t in times
])
total_imax_time = pulp.lpSum([
    adjusted_runtimes[i] * x[i, j, t, "IMAX"]
    for i in movie_ids for j in rooms for t in times
])
prob += total_3d_time <= MAX_3D_TIME
prob += total_imax_time <= MAX_IMAX_TIME

# (4) 简化的设备运行限制：每个放映厅每天总播放时长限制
for j in rooms:
    total_runtime_per_hall = pulp.lpSum([
        adjusted_runtimes[i] * x[i, j, t, v]
        for i in movie_ids for t in times for v in versions
        if df_cinema[df_cinema['room'] == j].iloc[0][v] == 1
    ])
    prob += total_runtime_per_hall <= 12 * 60  # 每天最多12小时

# (5) 题材播放次数限制
for theme, limits in THEME_LIMITS.items():
    count = pulp.lpSum([
        x[i, j, t, v]
        for i in movie_ids for j in rooms for t in times for v in versions
        if theme in df_movies.loc[df_movies['id'] == i, 'genres'].values[0]
    ])
    prob += count >= limits['min']
    prob += count <= limits['max']

# (6) 题材播放时间限制
for i in movie_ids:
    movie = df_movies[df_movies['id'] == i].iloc[0]
    genres = movie['genres'].split('|') if isinstance(movie['genres'], str) else []
    for j in rooms:
        for t in times:
            t_min = time_str_to_minutes(t)
            for v in versions:
                if df_cinema[df_cinema['room'] == j].iloc[0][v] == 0:
                    continue
                for theme in genres:
                    if theme in THEME_TIME_LIMITS:
                        time_limits = THEME_TIME_LIMITS[theme]
                        start = time_str_to_minutes(time_limits['start'])  # 支持 27:00
                        end = time_str_to_minutes(time_limits['end'])      # 支持 27:00
                        if not (start <= t_min <= end):
                            prob += x[i, j, t, v] == 0

# 约束 1: 同一放映厅在同一时间只能播放一部电影（无论版本）
for j in rooms:
    for t in times:
        prob += pulp.lpSum([x[i, j, t, v] for i in movie_ids for v in versions
                           if df_cinema[df_cinema['room'] == j].iloc[0][v] == 1]) <= 1

# 约束 2: 同一部电影在同一放映厅只能选择一个版本播放（整个排片周期内）
for i in movie_ids:
    for j in rooms:
        # 获取该厅支持的版本
        supported_versions = [v for v in versions if df_cinema[df_cinema['room'] == j].iloc[0][v] == 1]

        # 对于每对不同的版本，不能同时播放
        for v1_idx, v1 in enumerate(supported_versions):
            for v2 in supported_versions[v1_idx + 1:]:
                total_v1 = pulp.lpSum([x[i, j, t, v1] for t in times])
                total_v2 = pulp.lpSum([x[i, j, t, v2] for t in times])
                # 两个版本不能同时被选择
                prob += total_v1 + total_v2 <= 1

# ================== 6. 求解 ==================

print("开始求解...")

# 尝试多个求解器
solvers_to_try = [
    ("CBC", pulp.PULP_CBC_CMD(msg=1)),
    ("GLPK", pulp.GLPK_CMD(msg=1)),
]

# 如果 SCIP 可用，也尝试使用（但使用正确的参数）
try:
    solvers_to_try.append(("SCIP", pulp.SCIP_CMD(msg=1, timeLimit=7200)))
except:
    print("SCIP 求解器不可用，跳过")

solved = False
for solver_name, solver in solvers_to_try:
    try:
        print(f"尝试使用 {solver_name} 求解器...")
        prob.solve(solver)
        if prob.status == pulp.LpStatusOptimal:
            print(f"使用 {solver_name} 求解成功！")
            solved = True
            break
        elif prob.status == pulp.LpStatusInfeasible:
            print(f"{solver_name}: 模型不可行")
        else:
            print(f"{solver_name}: 求解状态 = {pulp.LpStatus[prob.status]}")
    except Exception as e:
        print(f"{solver_name} 求解器出错: {e}")
        continue

if not solved:
    print("所有求解器都失败了，尝试放宽约束...")

status = prob.status
print("最终求解状态:", pulp.LpStatus[status])

# ================== 7. 输出结果 ==================
print("\n" + "="*50)
print("求解状态:", pulp.LpStatus[prob.status])

if prob.status == pulp.LpStatusOptimal:
    print("目标值（净收益）:", pulp.value(prob.objective))

    # 提取排片结果
    results = []
    total_shows = 0

    for i in movie_ids:
        for j in rooms:
            for t in times:
                for v in versions:
                    # 检查放映厅是否支持该版本
                    if df_cinema[df_cinema['room'] == j].iloc[0][v] == 0:
                        continue

                    var = x[i, j, t, v]
                    if var.varValue is not None and var.varValue > 0.5:
                        hall = df_cinema[df_cinema['room'] == j].iloc[0]
                        movie = df_movies[df_movies['id'] == i].iloc[0]
                        attendance = int(hall['capacity'] * (movie['rating'] / 10))

                        results.append({
                            "放映厅": j,
                            "放映时间": t,
                            "电影ID": i,
                            "电影名称": movie['title'] if 'title' in movie else f"Movie_{i}",
                            "版本": v,
                            "预计上座人数": attendance,
                            "放映厅容量": hall['capacity']
                        })
                        total_shows += 1

    if results:
        result_df = pd.DataFrame(results)
        print(f"\n找到 {total_shows} 场排片:")
        print(result_df.to_string(index=False))

        # 保存结果
        result_df.to_csv("optimized_schedule.csv", index=False)
        print(f"\n排片方案已保存到 optimized_schedule.csv")

        # 统计信息
        print(f"\n统计信息:")
        print(f"总排片场次: {total_shows}")
        print(f"涉及电影数: {result_df['电影ID'].nunique()}")
        print(f"使用放映厅数: {result_df['放映厅'].nunique()}")

        # 按版本统计
        version_stats = result_df['版本'].value_counts()
        print(f"版本分布: {dict(version_stats)}")

    else:
        print("未找到任何排片方案！")
        # 创建空的 DataFrame 并保存
        result_df = pd.DataFrame()
        result_df.to_csv("optimized_schedule.csv", index=False)

elif prob.status == pulp.LpStatusInfeasible:
    print("模型不可行！请检查约束条件是否过于严格。")
    result_df = pd.DataFrame()
    result_df.to_csv("optimized_schedule.csv", index=False)

elif prob.status == pulp.LpStatusUnbounded:
    print("模型无界！请检查目标函数和约束。")
    result_df = pd.DataFrame()
    result_df.to_csv("optimized_schedule.csv", index=False)

else:
    print(f"求解未完成，状态: {pulp.LpStatus[prob.status]}")
    result_df = pd.DataFrame()
    result_df.to_csv("optimized_schedule.csv", index=False)


