# ChatBot API 使用文档

基于百炼平台的 ChatBot API 通信模块，支持多轮对话、流式输出和自定义参数配置。

## 功能特性

- ✅ **多轮对话支持**：基于 session_id 的会话管理，支持上下文连续对话
- ✅ **流式输出**：支持 Server-Sent Events (SSE) 流式响应处理
- ✅ **自定义参数配置**：支持温度、最大token数、模型选择等参数的动态设置
- ✅ **会话管理策略**：云端托管 + 本地缓存的混合管理模式
- ✅ **错误处理和重试**：完善的错误处理机制和指数退避重试策略
- ✅ **TypeScript 支持**：完整的类型定义，确保类型安全

## 快速开始

### 1. 环境配置

复制 `.env.example` 为 `.env` 并配置API密钥：

```bash
cp .env.example .env
```

编辑 `.env` 文件：

```env
# 百炼平台API配置
DASHSCOPE_API_KEY=your_dashscope_api_key_here
BAILIAN_APP_ID=your_bailian_app_id_here
```

### 2. 基本使用

#### 非流式对话

```typescript
import ChatBotAPIClient from '@/utils/chatbot/bailianai';

const client = new ChatBotAPIClient();

// 发送消息
const response = await client.sendMessage('你好，你是谁？');

if (response.success && response.data) {
  console.log('AI回复:', response.data.text);
  console.log('会话ID:', response.data.sessionId);
  
  // 继续对话
  const response2 = await client.sendMessage(
    '你有什么技能？',
    response.data.sessionId
  );
}
```

#### 流式对话

```typescript
const fullResponse = await client.sendStreamMessage(
  '请详细介绍一下B+树的数据结构',
  undefined, // 新会话
  (chunk) => {
    if (chunk.type === 'chunk' && chunk.data) {
      process.stdout.write(chunk.data.text); // 实时输出
    }
  }
);
```

### 3. API 端点使用

#### POST /api/chat

**请求参数：**

```typescript
interface ChatRequest {
  message: string;                    // 必需：用户消息
  sessionId?: string;                 // 可选：会话ID，用于多轮对话
  parameters?: {                      // 可选：API参数
    temperature?: number;             // 温度参数 (0-1)
    maxTokens?: number;              // 最大token数
    stream?: boolean;                // 是否流式输出
  };
  userPromptParams?: Record<string, string>; // 可选：自定义提示词参数
}
```

**响应格式：**

```typescript
interface ChatResponse {
  success: boolean;
  data?: {
    text: string;                     // AI回复内容
    sessionId: string;                // 会话ID
    metadata?: {                      // 元数据（如果有）
      module?: string;
      topic?: string;
      action?: {
        type: string;
        target: string;
        params?: Record<string, string>;
      };
    };
  };
  error?: {
    code: string;
    message: string;
  };
  usage?: {                          // Token使用情况
    inputTokens: number;
    outputTokens: number;
    totalTokens: number;
  };
}
```

### 4. 会话管理

```typescript
import { sessionManager } from '@/utils/chatbot/bailianai';

// 创建新会话
const sessionId = sessionManager.createSession();

// 获取会话信息
const sessionInfo = sessionManager.getSession(sessionId);

// 获取所有活跃会话
const activeSessions = sessionManager.getActiveSessions();

// 删除会话
sessionManager.deleteSession(sessionId);
```

## 高级功能

### 1. 自定义参数

```typescript
const response = await fetch('/api/chat', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    message: '美食推荐',
    parameters: {
      temperature: 0.7,
      maxTokens: 1000,
    },
    userPromptParams: {
      city: '北京',
      cuisine: '川菜',
    },
  }),
});
```

### 2. 错误处理

```typescript
import { BailianAIAPIError, ErrorType } from '@/types/chatbot/bailianai';

try {
  const response = await client.sendMessage('测试消息');
} catch (error) {
  if (error instanceof BailianAIAPIError) {
    switch (error.type) {
      case ErrorType.AUTHENTICATION_ERROR:
        console.error('认证失败:', error.message);
        break;
      case ErrorType.RATE_LIMIT_ERROR:
        console.error('请求频率过高:', error.message);
        break;
      case ErrorType.NETWORK_ERROR:
        console.error('网络错误:', error.message);
        break;
      default:
        console.error('未知错误:', error.message);
    }
  }
}
```

### 3. 请求取消

```typescript
const controller = new AbortController();

// 5秒后取消请求
setTimeout(() => controller.abort(), 5000);

const response = await client.sendMessage(
  '这是一个可能需要很长时间的问题',
  undefined,
  { signal: controller.signal }
);
```

## 测试和调试

### 1. 测试页面

访问 `/test-chatbot` 页面进行交互式测试：

- 支持流式和非流式输出切换
- 实时显示会话ID和Token使用情况
- 完整的错误信息显示

### 2. 运行示例

```typescript
import { runAllExamples } from '@/examples/chatbot-api-usage';

// 运行所有使用示例
await runAllExamples();
```

### 3. API配置验证

```typescript
import { validateAPIConfig } from '@/utils/chatbot/bailianai';

const { isValid, errors } = validateAPIConfig();
if (!isValid) {
  console.error('API配置错误:', errors);
}
```

## 配置选项

### 环境变量

| 变量名 | 必需 | 默认值 | 说明 |
|--------|------|--------|------|
| `DASHSCOPE_API_KEY` | ✅ | - | 百炼平台API密钥 |
| `BAILIAN_APP_ID` | ✅ | - | 百炼平台应用ID |
| `BAILIAN_BASE_URL` | ❌ | `https://dashscope.aliyuncs.com/api/v1/apps` | API基础URL |
| `BAILIAN_TIMEOUT` | ❌ | `30000` | 请求超时时间(ms) |
| `BAILIAN_MAX_RETRIES` | ❌ | `3` | 最大重试次数 |
| `BAILIAN_RETRY_DELAY` | ❌ | `1000` | 重试延迟时间(ms) |

### 默认配置

```typescript
export const DEFAULT_BAILIAN_CONFIG = {
  baseUrl: 'https://dashscope.aliyuncs.com/api/v1/apps',
  timeout: 30000,
  maxRetries: 3,
  retryDelay: 1000,
};

export const DEFAULT_RETRY_CONFIG = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffFactor: 2,
};
```

## 注意事项

1. **API密钥安全**：请勿在客户端代码中硬编码API密钥，始终使用环境变量
2. **请求频率**：注意API调用频率限制，避免触发限流
3. **会话管理**：会话会自动过期（24小时），请妥善处理会话失效的情况
4. **错误重试**：对于网络错误会自动重试，认证和参数错误不会重试
5. **流式响应**：流式响应需要正确处理连接断开和错误情况

## 故障排除

### 常见问题

1. **认证失败**
   - 检查 `DASHSCOPE_API_KEY` 是否正确配置
   - 确认API密钥是否有效且未过期

2. **应用ID错误**
   - 检查 `BAILIAN_APP_ID` 是否正确
   - 确认应用ID对应的应用是否存在且可用

3. **网络连接问题**
   - 检查网络连接是否正常
   - 确认防火墙是否允许访问百炼平台域名

4. **流式响应中断**
   - 检查网络稳定性
   - 适当增加超时时间配置

### 调试技巧

1. 启用详细日志输出
2. 使用测试页面进行交互式调试
3. 检查浏览器开发者工具的网络面板
4. 使用API配置验证函数检查配置

## 更新日志

- **v1.0.0** - 初始版本，支持基本聊天功能
- **v1.1.0** - 添加流式输出支持
- **v1.2.0** - 完善错误处理和重试机制
- **v1.3.0** - 添加会话管理功能
- **v1.4.0** - 添加自定义参数支持
